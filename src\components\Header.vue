<template>
  <header class="header">
    <div class="wrap">
      <div class="logo" @click="handleLogo">
        <img src="../assets/images/logo.png" />
        <h2>Toxicological data search</h2>
      </div>
      <div class="col">
        <nav class="navigation">
          <router-link to="/cas/search" class="nav-link">
            Toxicological Search
          </router-link>
          <router-link to="/chemistry/list" class="nav-link">
            Chemistry Database
          </router-link>
        </nav>
      </div>
    </div>
  </header>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "Header",
  components: {},
  setup() {
    const router = useRouter();
    const handleLogo = () => {
      console.log(router.currentRoute.value.path);
      const currentPath = router.currentRoute.value.path;
      if (currentPath.includes('/cas/detail')) {
        router.push("/cas/search");
      } else if (currentPath.includes('/chemistry/detail')) {
        router.push("/chemistry/list");
      } else {
        router.push("/cas/search");
      }
    }
    return {
      handleLogo
    };
  },
});
</script>

<style lang="scss" scoped>
.header {
  height: 70px;
  background: #fff;
  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
  width: 100%;

  .wrap {
    height: 70px;
    margin: 0 auto;
    justify-content: space-between;
    align-items: center;
    padding: 0 50px;
    display: flex;
    box-sizing: border-box;
    position: relative;
    width: 100%;
  }

  .logo {
    display: flex;
    align-items: center;
    cursor: pointer;

    img {
      height: 42px;
      width: 90px;
    }

    h2 {
      font-size: 20px;
      color: #333;
      font-weight: normal;
      padding-left: 30px;
    }
  }

  .col {
    display: flex;
    align-items: center;

    .navigation {
      display: flex;
      gap: 30px;
      margin-right: 30px;

      .nav-link {
        color: #333;
        text-decoration: none;
        font-size: 16px;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f7fa;
          color: #20558a;
        }

        &.router-link-active {
          background-color: #20558a;
          color: white;
        }
      }
    }

    .row {
      display: flex;
      flex-direction: column;
      margin-right: 20px;
      font-size: 14px;
      color: #666;
      border-left: 1px solid #d1d1d1;
      height: 40px;
      padding: 0 35px 0 12px;
      position: relative;

      span {
        font-size: 12px;
        color: #333;
        padding-bottom: 5px;
      }

      .systems {
        font-weight: normal;
        width: 80px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
      }

      .more {
        width: 18px;
        height: 18px;
        background: #eaeaea;
        color: #333;
        font-size: 12px;
        font-style: normal;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        display: inline-block;
        margin-left: 5px;
        margin-bottom: 2px;
      }

      &:first-child {
        border-left: none;
      }

      &:last-child {
        margin-right: 0;
        padding: 0 0 0 12px;
      }

      &.action {
        width: auto;
        flex-direction: row;
      }

      .logout {
        color: #999;
        font-size: 12px;
        position: absolute;
        right: 0;
        top: 0;
        font-style: normal;
        cursor: pointer;
      }
    }

    .setting,
    .download,
    .menu {
      display: flex;
      align-items: center;
      align-items: center;
      cursor: pointer;
      position: relative;

      i {
        width: 14px;
        height: 14px;
        background-size: 100% 100%;
        margin-right: 10px;
      }

      em {
        font-size: 14px;
        color: #333;
        font-style: normal;
        margin-top: -2px;
      }
    }

    .setting {
      margin-left: 30px;
      position: relative;

      b {
        position: absolute;
        left: 43px;
        bottom: -5px;
        color: #f00;
        font-size: 12px;
        font-weight: normal;
      }

      i {
        background: url("./../assets/images/preference.svg") no-repeat;
      }
    }

    .download {
      margin-left: 30px;

      a {
        color: #333;
        text-decoration: none;
      }

      i {
        background: url("./../assets/images/download.svg") no-repeat;
      }
    }

    .menu {
      position: relative;

      i {
        background: url("./../assets/images/menu.svg") no-repeat;
      }

      .menu-dialog {
        border-radius: 5px;
        box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.15);
        background: rgb(255, 255, 255);
        position: absolute;
        z-index: 12;
        left: 0;
        top: 40px;
        width: 130px;
        display: none;

        ul {
          padding: 10px 0;

          li {
            list-style: none;
            font-size: 14px;
            height: 30px;
            line-height: 30px;
            cursor: pointer;
            padding: 0 20px;

            &:hover {
              background: #f5f5f5;
              color: #ca4300;
            }
          }
        }
      }

      &:hover {
        .menu-dialog {
          display: block;
        }
      }
    }
  }

  .preference-dialog {
    border-radius: 5px;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.15);
    background: rgb(255, 255, 255);
    position: absolute;
    width: 237px;
    z-index: 12;
    right: 0;
    top: 47px;
    padding: 15px 20px;

    .footer {
      // margin: 20px 0;
      display: flex;
      justify-content: center;
    }

    .send-mail-list {
      width: 120px;
      height: 32px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>

{"name": "dqm-web", "code": "project_tic", "version": "24.07.29", "author": "Ken<PERSON>@sgs.com", "description": "DQM配置系统", "private": true, "type": "module", "scripts": {"local": "vite", "uat": "vite build --mode uat && rm -rf ./dist/*.map", "prod": "vite build --mode prod && rm -rf ./dist/*.map", "lint": "eslint src", "fix": "eslint src --fix", "prepare": "husky install"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@sentry/tracing": "7.43.0", "@sentry/vue": "7.43.0", "@sentry/webpack-plugin": "1.20.0", "@tic/components-service-v3": "^24.9.23", "@types/crypto-js": "^4.1.1", "axios": "^0.21.1", "element-plus": "2.3.8", "js-cookie": "^3.0.5", "terser": "^5.26.0", "vue": "^3.2.37", "vue-router": "^4.1.5", "vue3-json-viewer": "^2.2.2", "vuex": "^4.0.0-0"}, "devDependencies": {"@types/node": "18.7.18", "@types/webpack-env": "^1.17.0", "@vitejs/plugin-vue": "^3.1.0", "eslint": "7.32.0", "eslint-plugin-vue": "^9.26.0", "husky": "^8.0.1", "js-md5": "^0.7.3", "path-browserify": "^1.0.1", "prettier": "^3.3.2", "sass": "^1.89.2", "typescript": "^4.6.4", "vite": "^3.1.0", "vite-plugin-sentry": "^1.1.7", "vue-tsc": "^0.40.4"}}
<template>
    <div class="chemistry-detail" v-loading="data.loading">
        <!-- 返回按钮 -->
        <div class="back-section">
            <el-button @click="goBack" :icon="ArrowLeft">
                Back to List
            </el-button>
        </div>

        <div v-if="data.chemical" class="detail-content">
            <!-- 化学品基本信息 -->
            <div class="chemical-header">
                <div class="chemical-structure">
                    <img 
                        v-if="data.chemical.structure" 
                        :src="data.chemical.structure" 
                        :alt="data.chemical.name_en"
                        @error="handleImageError"
                    />
                    <div v-else class="no-structure">
                        <div class="icon">🧪</div>
                        <p>No Structure Available</p>
                    </div>
                </div>
                
                <div class="chemical-info">
                    <h1 class="chemical-name">{{ data.chemical.name_en }}</h1>
                    <h2 class="chemical-name-zh">{{ data.chemical.name_zh }}</h2>
                    
                    <div class="chemical-tags">
                        <el-tag :type="getCategoryType(data.chemical.category)" size="large">
                            {{ data.chemical.category }}
                        </el-tag>
                        <el-tag v-if="data.chemical.hazard_level" type="danger" size="large">
                            {{ data.chemical.hazard_level }}
                        </el-tag>
                    </div>

                    <div class="quick-actions">
                        <el-button type="primary" @click="copyToClipboard(data.chemical.cas_no)">
                            Copy CAS No.
                        </el-button>
                        <el-button @click="copyToClipboard(data.chemical.smiles)">
                            Copy SMILES
                        </el-button>
                        <el-button @click="downloadSDF">
                            Download SDF
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 详细信息表格 -->
            <div class="detail-sections">
                <!-- 基本属性 -->
                <el-card class="info-card">
                    <template #header>
                        <h3>Basic Properties</h3>
                    </template>
                    <div class="property-grid">
                        <div class="property-item">
                            <span class="label">CAS Number:</span>
                            <span class="value">{{ data.chemical.cas_no }}</span>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="copyToClipboard(data.chemical.cas_no)"
                            >
                                Copy
                            </el-button>
                        </div>
                        
                        <div class="property-item">
                            <span class="label">Molecular Formula:</span>
                            <span class="value formula">{{ data.chemical.formula }}</span>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="copyToClipboard(data.chemical.formula)"
                            >
                                Copy
                            </el-button>
                        </div>
                        
                        <div class="property-item">
                            <span class="label">Molecular Weight:</span>
                            <span class="value">{{ data.chemical.molecular_weight }} g/mol</span>
                        </div>
                        
                        <div class="property-item">
                            <span class="label">SMILES:</span>
                            <span class="value smiles">{{ data.chemical.smiles }}</span>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="copyToClipboard(data.chemical.smiles)"
                            >
                                Copy
                            </el-button>
                        </div>
                        
                        <div class="property-item" v-if="data.chemical.inchi">
                            <span class="label">InChI:</span>
                            <span class="value inchi">{{ data.chemical.inchi }}</span>
                            <el-button 
                                type="text" 
                                size="small" 
                                @click="copyToClipboard(data.chemical.inchi)"
                            >
                                Copy
                            </el-button>
                        </div>
                        
                        <div class="property-item" v-if="data.chemical.iupac_name">
                            <span class="label">IUPAC Name:</span>
                            <span class="value">{{ data.chemical.iupac_name }}</span>
                        </div>
                    </div>
                </el-card>

                <!-- 物理化学性质 -->
                <el-card class="info-card" v-if="data.chemical.physical_properties">
                    <template #header>
                        <h3>Physical & Chemical Properties</h3>
                    </template>
                    <div class="property-grid">
                        <div 
                            v-for="(value, key) in data.chemical.physical_properties" 
                            :key="key"
                            class="property-item"
                        >
                            <span class="label">{{ formatPropertyName(key) }}:</span>
                            <span class="value">{{ value }}</span>
                        </div>
                    </div>
                </el-card>

                <!-- 安全信息 -->
                <el-card class="info-card" v-if="data.chemical.safety_info">
                    <template #header>
                        <h3>Safety Information</h3>
                    </template>
                    <div class="safety-content">
                        <div v-if="data.chemical.safety_info.hazard_statements" class="safety-section">
                            <h4>Hazard Statements</h4>
                            <ul>
                                <li v-for="statement in data.chemical.safety_info.hazard_statements" :key="statement">
                                    {{ statement }}
                                </li>
                            </ul>
                        </div>
                        
                        <div v-if="data.chemical.safety_info.precautionary_statements" class="safety-section">
                            <h4>Precautionary Statements</h4>
                            <ul>
                                <li v-for="statement in data.chemical.safety_info.precautionary_statements" :key="statement">
                                    {{ statement }}
                                </li>
                            </ul>
                        </div>
                    </div>
                </el-card>

                <!-- 同义词 -->
                <el-card class="info-card" v-if="data.chemical.synonyms && data.chemical.synonyms.length">
                    <template #header>
                        <h3>Synonyms</h3>
                    </template>
                    <div class="synonyms-list">
                        <el-tag 
                            v-for="synonym in data.chemical.synonyms" 
                            :key="synonym"
                            class="synonym-tag"
                        >
                            {{ synonym }}
                        </el-tag>
                    </div>
                </el-card>

                <!-- 相关链接 -->
                <el-card class="info-card">
                    <template #header>
                        <h3>External Links</h3>
                    </template>
                    <div class="external-links">
                        <el-button 
                            type="primary" 
                            link 
                            @click="openExternalLink('pubchem', data.chemical.cas_no)"
                        >
                            PubChem
                        </el-button>
                        <el-button 
                            type="primary" 
                            link 
                            @click="openExternalLink('chemspider', data.chemical.cas_no)"
                        >
                            ChemSpider
                        </el-button>
                        <el-button 
                            type="primary" 
                            link 
                            @click="openExternalLink('nist', data.chemical.cas_no)"
                        >
                            NIST
                        </el-button>
                    </div>
                </el-card>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="!data.loading" class="error-state">
            <div class="error-content">
                <div class="error-icon">❌</div>
                <h3>Chemical Not Found</h3>
                <p>The requested chemical compound could not be found.</p>
                <el-button type="primary" @click="goBack">
                    Back to List
                </el-button>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
    name: 'ChemistryDetail',
    setup() {
        const router = useRouter()
        const route = useRoute()

        const data = reactive({
            chemical: null,
            loading: false
        }) as any

        // 模拟化学品详细数据
        const mockChemicalDetail = {
            id: '1',
            name_en: 'Acetone',
            name_zh: '丙酮',
            cas_no: '67-64-1',
            formula: 'C₃H₆O',
            molecular_weight: '58.08',
            category: 'organic',
            hazard_level: 'Flammable',
            structure: '/api/structure/67-64-1.png',
            smiles: 'CC(=O)C',
            inchi: 'InChI=1S/C3H6O/c1-3(2)4/h1-2H3',
            iupac_name: 'propan-2-one',
            physical_properties: {
                'boiling_point': '56.05°C',
                'melting_point': '-94.7°C',
                'density': '0.784 g/cm³',
                'flash_point': '-20°C',
                'vapor_pressure': '24.6 kPa at 20°C',
                'solubility': 'Miscible with water',
                'refractive_index': '1.359',
                'viscosity': '0.306 mPa·s at 25°C'
            },
            safety_info: {
                hazard_statements: [
                    'H225: Highly flammable liquid and vapour',
                    'H319: Causes serious eye irritation',
                    'H336: May cause drowsiness or dizziness'
                ],
                precautionary_statements: [
                    'P210: Keep away from heat, hot surfaces, sparks, open flames and other ignition sources',
                    'P233: Keep container tightly closed',
                    'P240: Ground and bond container and receiving equipment',
                    'P305+P351+P338: IF IN EYES: Rinse cautiously with water for several minutes'
                ]
            },
            synonyms: [
                '2-Propanone',
                'Dimethyl ketone',
                'Methyl ketone',
                'Propanone',
                'β-Ketopropane'
            ]
        }

        const loadChemicalDetail = async (id: string) => {
            data.loading = true
            try {
                // 模拟 API 调用
                await new Promise(resolve => setTimeout(resolve, 800))

                // 在实际应用中，这里会是真实的 API 调用
                if (id === '1') {
                    data.chemical = mockChemicalDetail
                } else {
                    data.chemical = null
                }
            } catch (error) {
                ElMessage.error('Failed to load chemical details')
                console.error('Error loading chemical details:', error)
                data.chemical = null
            } finally {
                data.loading = false
            }
        }

        const goBack = () => {
            router.back()
        }

        const handleImageError = (event: Event) => {
            const target = event.target as HTMLImageElement
            target.style.display = 'none'
            const parent = target.parentElement
            if (parent) {
                parent.innerHTML = `
                    <div class="no-structure">
                        <div class="icon">🧪</div>
                        <p>No Structure Available</p>
                    </div>
                `
            }
        }

        const getCategoryType = (category: string) => {
            const types: Record<string, string> = {
                'organic': 'success',
                'inorganic': 'info',
                'pharmaceutical': 'warning',
                'industrial': 'danger'
            }
            return types[category] || ''
        }

        const copyToClipboard = async (text: string) => {
            try {
                await navigator.clipboard.writeText(text)
                ElMessage.success('Copied to clipboard')
            } catch (error) {
                ElMessage.error('Failed to copy to clipboard')
            }
        }

        const formatPropertyName = (key: string) => {
            return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        }

        const downloadSDF = () => {
            // 模拟 SDF 文件下载
            ElMessage.info('SDF download functionality would be implemented here')
        }

        const openExternalLink = (platform: string, casNo: string) => {
            const urls: Record<string, string> = {
                'pubchem': `https://pubchem.ncbi.nlm.nih.gov/compound/${casNo}`,
                'chemspider': `https://www.chemspider.com/Search.aspx?q=${casNo}`,
                'nist': `https://webbook.nist.gov/cgi/cbook.cgi?ID=${casNo}`
            }

            if (urls[platform]) {
                window.open(urls[platform], '_blank')
            }
        }

        onMounted(() => {
            const id = route.params.id as string
            if (id) {
                loadChemicalDetail(id)
            }
        })

        return {
            data,
            ArrowLeft,
            goBack,
            handleImageError,
            getCategoryType,
            copyToClipboard,
            formatPropertyName,
            downloadSDF,
            openExternalLink
        }
    }
})
</script>

<style lang="scss" scoped>
.chemistry-detail {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding: 20px;

    .back-section {
        max-width: 1200px;
        margin: 0 auto 20px;
    }

    .detail-content {
        max-width: 1200px;
        margin: 0 auto;

        .chemical-header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
            display: flex;
            gap: 40px;
            align-items: flex-start;

            .chemical-structure {
                width: 300px;
                height: 300px;
                background: #f8f9fa;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                border: 2px solid #e9ecef;

                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }

                .no-structure {
                    text-align: center;
                    color: #999;

                    .icon {
                        font-size: 48px;
                        margin-bottom: 12px;
                    }

                    p {
                        font-style: italic;
                        margin: 0;
                    }
                }
            }

            .chemical-info {
                flex: 1;

                .chemical-name {
                    font-size: 32px;
                    font-weight: 700;
                    color: #333;
                    margin-bottom: 8px;
                    line-height: 1.2;
                }

                .chemical-name-zh {
                    font-size: 20px;
                    color: #666;
                    margin-bottom: 20px;
                    font-weight: 500;
                }

                .chemical-tags {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 30px;
                    flex-wrap: wrap;
                }

                .quick-actions {
                    display: flex;
                    gap: 12px;
                    flex-wrap: wrap;
                }
            }
        }

        .detail-sections {
            display: grid;
            gap: 24px;

            .info-card {
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                border-radius: 12px;

                :deep(.el-card__header) {
                    background: #f8f9fa;
                    border-bottom: 1px solid #e9ecef;

                    h3 {
                        margin: 0;
                        font-size: 18px;
                        font-weight: 600;
                        color: #333;
                    }
                }

                .property-grid {
                    display: grid;
                    gap: 16px;

                    .property-item {
                        display: flex;
                        align-items: center;
                        padding: 12px 0;
                        border-bottom: 1px solid #f0f0f0;

                        &:last-child {
                            border-bottom: none;
                        }

                        .label {
                            font-weight: 600;
                            color: #555;
                            width: 180px;
                            flex-shrink: 0;
                        }

                        .value {
                            flex: 1;
                            color: #333;
                            margin-right: 12px;

                            &.formula {
                                font-family: 'Times New Roman', serif;
                                font-size: 16px;
                            }

                            &.smiles, &.inchi {
                                font-family: monospace;
                                font-size: 14px;
                                word-break: break-all;
                                background: #f8f9fa;
                                padding: 4px 8px;
                                border-radius: 4px;
                            }
                        }
                    }
                }

                .safety-content {
                    .safety-section {
                        margin-bottom: 24px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        h4 {
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 12px;
                        }

                        ul {
                            margin: 0;
                            padding-left: 20px;

                            li {
                                margin-bottom: 8px;
                                line-height: 1.5;
                                color: #555;

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }

                .synonyms-list {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;

                    .synonym-tag {
                        margin: 0;
                    }
                }

                .external-links {
                    display: flex;
                    gap: 16px;
                    flex-wrap: wrap;
                }
            }
        }
    }

    .error-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60vh;

        .error-content {
            text-align: center;
            max-width: 400px;

            .error-icon {
                font-size: 64px;
                margin-bottom: 24px;
            }

            h3 {
                font-size: 24px;
                color: #333;
                margin-bottom: 16px;
            }

            p {
                color: #666;
                margin-bottom: 24px;
                line-height: 1.6;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .chemistry-detail {
        padding: 16px;

        .detail-content {
            .chemical-header {
                flex-direction: column;
                padding: 24px;
                gap: 24px;

                .chemical-structure {
                    width: 100%;
                    height: 250px;
                }

                .chemical-info {
                    .chemical-name {
                        font-size: 24px;
                    }

                    .chemical-name-zh {
                        font-size: 16px;
                    }

                    .quick-actions {
                        flex-direction: column;

                        .el-button {
                            width: 100%;
                        }
                    }
                }
            }

            .detail-sections {
                .info-card {
                    .property-grid {
                        .property-item {
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 8px;

                            .label {
                                width: 100%;
                                font-size: 14px;
                            }

                            .value {
                                margin-right: 0;
                            }
                        }
                    }

                    .external-links {
                        flex-direction: column;

                        .el-button {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
}
</style>

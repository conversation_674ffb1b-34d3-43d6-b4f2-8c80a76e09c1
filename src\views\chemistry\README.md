# Chemistry Module

这个模块包含了化学品数据库的列表和详情页面。

## 文件结构

```
src/views/chemistry/
├── List.vue      # 化学品列表页面
├── Detail.vue    # 化学品详情页面
└── README.md     # 说明文档
```

## 功能特性

### List.vue (列表页面)
- **搜索功能**: 支持按化学品名称、CAS号、分子式、SMILES等搜索
- **筛选功能**: 按分类筛选（有机、无机、药物、工业等）
- **排序功能**: 按名称、CAS号、添加时间排序
- **视图切换**: 支持网格视图和列表视图
- **分页功能**: 支持分页浏览
- **响应式设计**: 适配移动端和桌面端

### Detail.vue (详情页面)
- **基本信息**: 显示化学品的基本属性（CAS号、分子式、分子量等）
- **结构显示**: 显示化学结构图
- **物理化学性质**: 显示详细的物理化学参数
- **安全信息**: 显示危险性声明和预防措施
- **同义词**: 显示化学品的其他名称
- **外部链接**: 链接到PubChem、ChemSpider、NIST等外部数据库
- **复制功能**: 一键复制CAS号、SMILES等信息
- **下载功能**: 支持下载SDF文件

## 路由配置

已在 `src/router/static.ts` 中添加了以下路由：

```typescript
{
    path: "/chemistry/list",
    name: "ChemistryList",
    meta: {
        title: '化学品数据库',
        requireAuth: true,
        isFull: false
    },
    component: () => import("../views/chemistry/List.vue"),
},
{
    path: "/chemistry/detail/:id",
    name: "ChemistryDetail",
    meta: {
        title: '化学品详情',
        requireAuth: true,
        isFull: false
    },
    component: () => import("../views/chemistry/Detail.vue"),
}
```

## 导航集成

已在 `src/components/Header.vue` 中添加了导航链接：
- "Chemistry Database" 链接到列表页面
- 更新了logo点击逻辑以支持chemistry路由

## 样式特性

- 使用 SCSS 预处理器
- 响应式设计，支持移动端
- 现代化的卡片式布局
- 平滑的过渡动画
- 一致的颜色主题

## 数据结构

### 化学品对象结构
```typescript
interface Chemical {
    id: string
    name_en: string          // 英文名
    name_zh: string          // 中文名
    cas_no: string           // CAS号
    formula: string          // 分子式
    molecular_weight: string // 分子量
    category: string         // 分类
    structure?: string       // 结构图URL
    smiles: string          // SMILES字符串
    inchi?: string          // InChI字符串
    iupac_name?: string     // IUPAC名称
    physical_properties?: object  // 物理化学性质
    safety_info?: object    // 安全信息
    synonyms?: string[]     // 同义词
}
```

## 使用说明

1. **访问列表页面**: 导航到 `/chemistry/list`
2. **搜索化学品**: 使用搜索框输入关键词
3. **筛选结果**: 使用分类和排序选项
4. **查看详情**: 点击任意化学品卡片
5. **复制信息**: 在详情页面点击复制按钮
6. **外部链接**: 点击外部数据库链接获取更多信息

## 技术栈

- Vue 3 + TypeScript
- Element Plus UI组件库
- Vue Router 4
- SCSS样式预处理器
- 响应式设计

## 后续开发

- [ ] 集成真实的化学品数据API
- [ ] 添加高级搜索功能
- [ ] 实现收藏功能
- [ ] 添加化学品比较功能
- [ ] 集成更多外部数据源
- [ ] 添加数据导出功能

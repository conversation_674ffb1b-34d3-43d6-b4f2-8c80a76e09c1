const staticRouter = [
    {
        path: "/",
        name: "/",
        redirect: {
            name: "CasSearch",
        }
    },
    {
        path: "/layout",
        name: "Layout",
        component: () => import(/* webpackChunkName: "layout" */ "../views/Layout.vue"),
        children: [
            {
                path: "/cas/search",
                name: "CasSearch",
                meta: {
                    title: '化学品信息查询',
                    requireAuth: true,
                    isFull: false
                },
                component: () => import(/* webpackChunkName: "cas" */ "../views/cas/Search.vue"),
            },
            {
                path: "/cas/detail/:id",
                name: "CasDetail",
                meta: {
                    title: '化学品信息详情',
                    requireAuth: true,
                    isFull: false
                },
                component: () => import(/* webpackChunkName: "cas" */ "../views/cas/Detail.vue"),
            },
            {
                path: "/chemistry/list",
                name: "ChemistryList",
                meta: {
                    title: '化学品数据库',
                    requireAuth: true,
                    isFull: false
                },
                component: () => import(/* webpackChunkName: "chemistry" */ "../views/chemistry/List.vue"),
            },
            {
                path: "/chemistry/detail/:id",
                name: "ChemistryDetail",
                meta: {
                    title: '化学品详情',
                    requireAuth: true,
                    isFull: false
                },
                component: () => import(/* webpackChunkName: "chemistry" */ "../views/chemistry/Detail.vue"),
            }
        ]
    },
    // {
    //     path: "/layoutAF",
    //     name: "LayoutAF",
    //     component: () => import(/* webpackChunkName: "layout" */ "../views/LayoutAF.vue"),
    //     children: [
    //         {
    //             path: "/af/search",
    //             name: "AfSearch",
    //             meta: {
    //                 title: '同竞行业相关信息查询',
    //                 requireAuth: true,
    //                 isFull: false,
    //                 isAF: true
    //             },
    //             component: () => import(/* webpackChunkName: "cas" */ "../views/af/Search.vue"),
    //         },
    //         {
    //             path: "/af/company/:id",
    //             name: "AfCompanyDetail",
    //             meta: {
    //                 title: '公司信息详情',
    //                 requireAuth: true,
    //                 isFull: false,
    //                 isAF: true
    //             },
    //             component: () => import(/* webpackChunkName: "cas" */ "../views/af/Company.vue"),
    //         },
    //         {
    //             path: "/af/testItem",
    //             name: "AfTestItemDetail",
    //             meta: {
    //                 title: '测试项目详情',
    //                 requireAuth: true,
    //                 isFull: false,
    //                 isAF: true
    //             },
    //             component: () => import(/* webpackChunkName: "cas" */ "../views/af/TestItem.vue"),
    //         },
    //     ]
    // },
    {
        path: "/activity/record",
        name: "ActivityRecord",
        meta: {
            title: '活动管理',
            requireAuth: true,
            isFull: false,
            isAF: true
        },
        component: () => import(/* webpackChunkName: "cas" */ "../views/activity/Record.vue"),
    },
]

export {
    staticRouter
}
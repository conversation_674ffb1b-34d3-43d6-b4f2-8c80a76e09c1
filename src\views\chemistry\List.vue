<template>
    <div class="chemistry-list">
        <div class="chemistry-wrap">
            <div class="title">Chemistry Database</div>
            <div class="descript">Browse and search chemical compounds database</div>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-box">
                    <el-input 
                        v-model="data.searchInput" 
                        :placeholder="'Enter chemical name, CAS number, or formula...'" 
                        size="large"
                        @keyup.enter="handleSearch"
                    >
                        <template #prepend>
                            <el-select v-model="data.searchType" placeholder="Search Type" style="width: 150px">
                                <el-option 
                                    v-for="type in data.searchOptions" 
                                    :key="type.value"
                                    :label="type.label" 
                                    :value="type.value"
                                />
                            </el-select>
                        </template>
                        <template #append>
                            <el-button :icon="Search" @click="handleSearch">Search</el-button>
                        </template>
                    </el-input>
                </div>
                
                <!-- 筛选器 -->
                <div class="filters">
                    <el-select v-model="data.categoryFilter" placeholder="Category" clearable>
                        <el-option label="All Categories" value="" />
                        <el-option label="Organic" value="organic" />
                        <el-option label="Inorganic" value="inorganic" />
                        <el-option label="Pharmaceutical" value="pharmaceutical" />
                        <el-option label="Industrial" value="industrial" />
                    </el-select>
                    
                    <el-select v-model="data.sortBy" placeholder="Sort By">
                        <el-option label="Name A-Z" value="name_asc" />
                        <el-option label="Name Z-A" value="name_desc" />
                        <el-option label="CAS Number" value="cas_asc" />
                        <el-option label="Recently Added" value="date_desc" />
                    </el-select>
                </div>
            </div>
        </div>

        <!-- 结果列表 -->
        <div class="results-section" v-if="data.chemicals.length">
            <div class="results-header">
                <div class="results-count">
                    Found {{ data.total }} chemicals
                    <span v-if="data.searchInput">" for "{{ data.searchInput }}"</span>
                </div>
                <div class="view-toggle">
                    <el-button-group>
                        <el-button 
                            :type="data.viewMode === 'grid' ? 'primary' : ''" 
                            @click="data.viewMode = 'grid'"
                        >
                            Grid
                        </el-button>
                        <el-button 
                            :type="data.viewMode === 'list' ? 'primary' : ''" 
                            @click="data.viewMode = 'list'"
                        >
                            List
                        </el-button>
                    </el-button-group>
                </div>
            </div>

            <!-- 网格视图 -->
            <div v-if="data.viewMode === 'grid'" class="grid-view">
                <div 
                    v-for="chemical in data.chemicals" 
                    :key="chemical.id"
                    class="chemical-card"
                    @click="handleDetail(chemical.id)"
                >
                    <div class="card-image">
                        <img 
                            v-if="chemical.structure" 
                            :src="chemical.structure" 
                            :alt="chemical.name_en"
                            @error="handleImageError"
                        />
                        <div v-else class="no-structure">No Structure</div>
                    </div>
                    <div class="card-content">
                        <h3 class="chemical-name">{{ chemical.name_en }}</h3>
                        <p class="chemical-name-zh">{{ chemical.name_zh }}</p>
                        <div class="chemical-info">
                            <span class="cas-number">CAS: {{ chemical.cas_no }}</span>
                            <span class="formula">{{ chemical.formula }}</span>
                        </div>
                        <div class="chemical-category">
                            <el-tag size="small" :type="getCategoryType(chemical.category)">
                                {{ chemical.category }}
                            </el-tag>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 列表视图 -->
            <div v-else class="list-view">
                <div 
                    v-for="chemical in data.chemicals" 
                    :key="chemical.id"
                    class="chemical-item"
                    @click="handleDetail(chemical.id)"
                >
                    <div class="item-image">
                        <img 
                            v-if="chemical.structure" 
                            :src="chemical.structure" 
                            :alt="chemical.name_en"
                            @error="handleImageError"
                        />
                        <div v-else class="no-structure">No Structure</div>
                    </div>
                    <div class="item-content">
                        <div class="item-header">
                            <h3 class="chemical-name">{{ chemical.name_en }}</h3>
                            <el-tag size="small" :type="getCategoryType(chemical.category)">
                                {{ chemical.category }}
                            </el-tag>
                        </div>
                        <p class="chemical-name-zh">{{ chemical.name_zh }}</p>
                        <div class="chemical-details">
                            <div class="detail-item">
                                <span class="label">CAS No:</span>
                                <span class="value">{{ chemical.cas_no }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Formula:</span>
                                <span class="value">{{ chemical.formula }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Molecular Weight:</span>
                                <span class="value">{{ chemical.molecular_weight }}</span>
                            </div>
                            <div class="detail-item" v-if="chemical.smiles">
                                <span class="label">SMILES:</span>
                                <span class="value smiles">{{ chemical.smiles }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-section">
                <el-pagination
                    v-model:current-page="data.currentPage"
                    v-model:page-size="data.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="data.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
            <div class="empty-content">
                <div class="empty-icon">🧪</div>
                <h3>No chemicals found</h3>
                <p v-if="data.searchInput">
                    No results found for "{{ data.searchInput }}". Try adjusting your search terms.
                </p>
                <p v-else>
                    Start by searching for chemical compounds or browse by category.
                </p>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
    name: 'ChemistryList',
    setup() {
        const router = useRouter()

        const data = reactive({
            searchInput: '',
            searchType: 'name',
            searchOptions: [
                { label: 'Chemical Name', value: 'name' },
                { label: 'CAS Number', value: 'cas' },
                { label: 'Formula', value: 'formula' },
                { label: 'SMILES', value: 'smiles' }
            ],
            categoryFilter: '',
            sortBy: 'name_asc',
            viewMode: 'grid',
            chemicals: [],
            total: 0,
            currentPage: 1,
            pageSize: 20,
            loading: false
        }) as any

        // 模拟数据
        const mockChemicals = [
            {
                id: '1',
                name_en: 'Acetone',
                name_zh: '丙酮',
                cas_no: '67-64-1',
                formula: 'C3H6O',
                molecular_weight: '58.08',
                category: 'organic',
                structure: '/api/structure/67-64-1.png',
                smiles: 'CC(=O)C'
            },
            {
                id: '2',
                name_en: 'Benzene',
                name_zh: '苯',
                cas_no: '71-43-2',
                formula: 'C6H6',
                molecular_weight: '78.11',
                category: 'organic',
                structure: '/api/structure/71-43-2.png',
                smiles: 'c1ccccc1'
            },
            {
                id: '3',
                name_en: 'Sodium Chloride',
                name_zh: '氯化钠',
                cas_no: '7647-14-5',
                formula: 'NaCl',
                molecular_weight: '58.44',
                category: 'inorganic',
                structure: null,
                smiles: '[Na+].[Cl-]'
            }
        ]

        const loadChemicals = async () => {
            data.loading = true
            try {
                // 模拟 API 调用
                await new Promise(resolve => setTimeout(resolve, 500))

                let filteredChemicals = [...mockChemicals]

                // 搜索过滤
                if (data.searchInput) {
                    const searchTerm = data.searchInput.toLowerCase()
                    filteredChemicals = filteredChemicals.filter(chemical => {
                        switch (data.searchType) {
                            case 'name':
                                return chemical.name_en.toLowerCase().includes(searchTerm) ||
                                       chemical.name_zh.includes(searchTerm)
                            case 'cas':
                                return chemical.cas_no.includes(searchTerm)
                            case 'formula':
                                return chemical.formula.toLowerCase().includes(searchTerm)
                            case 'smiles':
                                return chemical.smiles.toLowerCase().includes(searchTerm)
                            default:
                                return true
                        }
                    })
                }

                // 分类过滤
                if (data.categoryFilter) {
                    filteredChemicals = filteredChemicals.filter(chemical =>
                        chemical.category === data.categoryFilter
                    )
                }

                // 排序
                filteredChemicals.sort((a, b) => {
                    switch (data.sortBy) {
                        case 'name_asc':
                            return a.name_en.localeCompare(b.name_en)
                        case 'name_desc':
                            return b.name_en.localeCompare(a.name_en)
                        case 'cas_asc':
                            return a.cas_no.localeCompare(b.cas_no)
                        default:
                            return 0
                    }
                })

                data.total = filteredChemicals.length

                // 分页
                const start = (data.currentPage - 1) * data.pageSize
                const end = start + data.pageSize
                data.chemicals = filteredChemicals.slice(start, end)

            } catch (error) {
                ElMessage.error('Failed to load chemicals')
                console.error('Error loading chemicals:', error)
            } finally {
                data.loading = false
            }
        }

        const handleSearch = () => {
            data.currentPage = 1
            loadChemicals()
        }

        const handleDetail = (id: string) => {
            router.push(`/chemistry/detail/${id}`)
        }

        const handleImageError = (event: Event) => {
            const target = event.target as HTMLImageElement
            target.style.display = 'none'
            const parent = target.parentElement
            if (parent) {
                parent.innerHTML = '<div class="no-structure">No Structure</div>'
            }
        }

        const getCategoryType = (category: string) => {
            const types: Record<string, string> = {
                'organic': 'success',
                'inorganic': 'info',
                'pharmaceutical': 'warning',
                'industrial': 'danger'
            }
            return types[category] || ''
        }

        const handleSizeChange = (size: number) => {
            data.pageSize = size
            data.currentPage = 1
            loadChemicals()
        }

        const handleCurrentChange = (page: number) => {
            data.currentPage = page
            loadChemicals()
        }

        // 监听筛选条件变化
        watch([() => data.categoryFilter, () => data.sortBy], () => {
            data.currentPage = 1
            loadChemicals()
        })

        onMounted(() => {
            loadChemicals()
        })

        return {
            data,
            Search,
            handleSearch,
            handleDetail,
            handleImageError,
            getCategoryType,
            handleSizeChange,
            handleCurrentChange
        }
    }
})
</script>

<style lang="scss" scoped>
.chemistry-list {
    min-height: 100vh;
    background-color: #f5f7fa;

    .chemistry-wrap {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        text-align: center;

        .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .descript {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .search-section {
            max-width: 800px;
            margin: 0 auto;

            .search-box {
                margin-bottom: 20px;
            }

            .filters {
                display: flex;
                gap: 16px;
                justify-content: center;
                flex-wrap: wrap;

                .el-select {
                    width: 150px;
                }
            }
        }
    }

    .results-section {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 16px;

            .results-count {
                font-size: 16px;
                color: #666;
            }
        }

        .grid-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 40px;

            .chemical-card {
                background: white;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-4px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                }

                .card-image {
                    height: 200px;
                    background: #f8f9fa;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-bottom: 1px solid #eee;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }

                    .no-structure {
                        color: #999;
                        font-style: italic;
                    }
                }

                .card-content {
                    padding: 20px;

                    .chemical-name {
                        font-size: 18px;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 8px;
                        line-height: 1.4;
                    }

                    .chemical-name-zh {
                        color: #666;
                        margin-bottom: 12px;
                        font-size: 14px;
                    }

                    .chemical-info {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                        margin-bottom: 12px;

                        .cas-number, .formula {
                            font-size: 13px;
                            color: #888;
                        }
                    }

                    .chemical-category {
                        display: flex;
                        justify-content: flex-end;
                    }
                }
            }
        }

        .list-view {
            .chemical-item {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                margin-bottom: 16px;
                padding: 20px;
                display: flex;
                gap: 20px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                }

                .item-image {
                    width: 120px;
                    height: 120px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }

                    .no-structure {
                        color: #999;
                        font-style: italic;
                        font-size: 12px;
                    }
                }

                .item-content {
                    flex: 1;

                    .item-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-start;
                        margin-bottom: 8px;

                        .chemical-name {
                            font-size: 20px;
                            font-weight: 600;
                            color: #333;
                            margin: 0;
                        }
                    }

                    .chemical-name-zh {
                        color: #666;
                        margin-bottom: 16px;
                        font-size: 14px;
                    }

                    .chemical-details {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 8px;

                        .detail-item {
                            display: flex;
                            font-size: 14px;

                            .label {
                                font-weight: 500;
                                color: #555;
                                width: 120px;
                                flex-shrink: 0;
                            }

                            .value {
                                color: #777;

                                &.smiles {
                                    font-family: monospace;
                                    font-size: 12px;
                                    word-break: break-all;
                                }
                            }
                        }
                    }
                }
            }
        }

        .pagination-section {
            display: flex;
            justify-content: center;
            margin-top: 40px;
        }
    }

    .empty-state {
        text-align: center;
        padding: 80px 20px;

        .empty-content {
            max-width: 400px;
            margin: 0 auto;

            .empty-icon {
                font-size: 64px;
                margin-bottom: 24px;
            }

            h3 {
                font-size: 24px;
                color: #333;
                margin-bottom: 16px;
            }

            p {
                color: #666;
                line-height: 1.6;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .chemistry-list {
        .chemistry-wrap {
            padding: 40px 20px;

            .title {
                font-size: 32px;
            }

            .search-section {
                .filters {
                    flex-direction: column;
                    align-items: center;

                    .el-select {
                        width: 200px;
                    }
                }
            }
        }

        .results-section {
            padding: 20px;

            .results-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .grid-view {
                grid-template-columns: 1fr;
            }

            .list-view {
                .chemical-item {
                    flex-direction: column;
                    gap: 16px;

                    .item-image {
                        width: 100%;
                        height: 200px;
                    }

                    .chemical-details {
                        grid-template-columns: 1fr;
                    }
                }
            }
        }
    }
}
</style>
